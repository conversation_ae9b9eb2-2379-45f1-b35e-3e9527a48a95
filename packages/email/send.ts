import { render } from '@react-email/components';
import type { ReactElement } from 'react';
import {} from './i18n';
import { sendgrid } from './index';
import { keys } from './keys';
import { generateReturnLabelPDF } from './utils/pdf-generator';

const env = keys();

export interface EmailAttachment {
  content: string; // base64 encoded content
  filename: string;
  type: string; // MIME type
  disposition: 'attachment' | 'inline';
}

export interface SendEmailOptions {
  to: string | string[];
  subject: string;
  template: ReactElement;
  from?: string;
  attachments?: EmailAttachment[];
}

export async function sendEmail({
  to,
  subject,
  template,
  from = env.SENDGRID_FROM,
  attachments,
}: SendEmailOptions) {
  try {
    const html = await render(template);

    const msg = {
      to: Array.isArray(to) ? to : [to],
      from,
      subject,
      html,
      ...(attachments && attachments.length > 0 && { attachments }),
    };

    const result = await sendgrid.send(msg);
    return { success: true, result };
  } catch (error) {
    console.error('Failed to send email:', error);
    return { success: false, error };
  }
}

// Specific email sending functions for each template type
export async function sendReturnRequestAutoApprovedEmail(
  to: string,
  returnNumber: string,
  refundDays?: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    customerName?: string;
    customerAddress?: string;
    companyName?: string;
    companyAddress?: string;
    returnDepartment?: string;
  },
  locale = 'ja'
) {
  const { ReturnRequestAutoApprovedEmail } = await import(
    './templates/return-request-auto-approved'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );

  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  // Only attempt PDF generation in server environments
  if (
    returnLabelData?.returnLabelOption === 'print' &&
    typeof window === 'undefined'
  ) {
    try {
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        customerName: returnLabelData.customerName,
        customerAddress: returnLabelData.customerAddress,
        companyName: returnLabelData.companyName,
        companyAddress: returnLabelData.companyAddress,
        returnDepartment: returnLabelData.returnDepartment,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  // Get localized subject
  const subject = await getLocalizedEmailSubject(
    'return_request_auto_approved',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ReturnRequestAutoApprovedEmail({
      returnNumber,
      refundDays,
      dictionary,
    }),
    attachments,
  });
}

export async function sendReturnRequestManualReceivedEmail(
  to: string,
  returnNumber: string,
  reviewDays?: string,
  locale = 'ja'
) {
  const { ReturnRequestManualReceivedEmail } = await import(
    './templates/return-request-manual-received'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );

  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  // Get localized subject
  const subject = await getLocalizedEmailSubject(
    'return_request_manual_received',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ReturnRequestManualReceivedEmail({
      returnNumber,
      reviewDays,
      dictionary,
    }),
  });
}

export async function sendReturnRequestManualApprovedEmail(
  to: string,
  returnNumber: string,
  refundDays?: string,
  returnInstructions?: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    customerName?: string;
    customerAddress?: string;
    companyName?: string;
    companyAddress?: string;
    returnDepartment?: string;
  }
) {
  const { ReturnRequestManualApprovedEmail } = await import(
    './templates/return-request-manual-approved'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  // Only attempt PDF generation in server environments
  if (
    returnLabelData?.returnLabelOption === 'print' &&
    typeof window === 'undefined'
  ) {
    try {
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        customerName: returnLabelData.customerName,
        customerAddress: returnLabelData.customerAddress,
        companyName: returnLabelData.companyName,
        companyAddress: returnLabelData.companyAddress,
        returnDepartment: returnLabelData.returnDepartment,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  return sendEmail({
    to,
    subject: `Return Request #${returnNumber} Approved`,
    template: ReturnRequestManualApprovedEmail({
      returnNumber,
      refundDays,
      returnInstructions,
    }),
    attachments,
  });
}

export async function sendExchangeRequestAutoApprovedEmail(
  to: string,
  returnNumber: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    customerName?: string;
    customerAddress?: string;
    companyName?: string;
    companyAddress?: string;
    returnDepartment?: string;
  },
  locale = 'ja'
) {
  const { ExchangeRequestAutoApprovedEmail } = await import(
    './templates/exchange-request-auto-approved'
  );
  const { loadEmailTranslations, getLocalizedEmailSubject } = await import(
    './i18n'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  // Only attempt PDF generation in server environments
  if (
    returnLabelData?.returnLabelOption === 'print' &&
    typeof window === 'undefined'
  ) {
    try {
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        customerName: returnLabelData.customerName,
        customerAddress: returnLabelData.customerAddress,
        companyName: returnLabelData.companyName,
        companyAddress: returnLabelData.companyAddress,
        returnDepartment: returnLabelData.returnDepartment,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  // Load translations for the specified locale
  const dictionary = await loadEmailTranslations(locale);

  // Get localized subject
  const subject = await getLocalizedEmailSubject(
    'exchange_request_auto_approved',
    locale,
    { returnNumber }
  );

  return sendEmail({
    to,
    subject,
    template: ExchangeRequestAutoApprovedEmail({
      returnNumber,
      dictionary,
    }),
    attachments,
  });
}

export async function sendExchangeItemShippedEmail(
  to: string,
  returnNumber: string,
  trackingNumber: string
) {
  const { ExchangeItemShippedEmail } = await import(
    './templates/exchange-item-shipped'
  );

  return sendEmail({
    to,
    subject: `Replacement Item Shipped - Request #${returnNumber}`,
    template: ExchangeItemShippedEmail({ returnNumber, trackingNumber }),
  });
}

export async function sendExchangeItemReceivedEmail(
  to: string,
  returnNumber: string
) {
  const { ExchangeItemReceivedEmail } = await import(
    './templates/exchange-item-received'
  );

  return sendEmail({
    to,
    subject: `Exchange Complete - Request #${returnNumber}`,
    template: ExchangeItemReceivedEmail({ returnNumber }),
  });
}

export async function sendExchangeRequestManualReceivedEmail(
  to: string,
  returnNumber: string
) {
  const { ExchangeRequestManualReceivedEmail } = await import(
    './templates/exchange-request-manual-received'
  );

  return sendEmail({
    to,
    subject: `Exchange Request #${returnNumber} Received`,
    template: ExchangeRequestManualReceivedEmail({ returnNumber }),
  });
}

export async function sendExchangeRequestManualApprovedEmail(
  to: string,
  returnNumber: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    customerName?: string;
    customerAddress?: string;
    companyName?: string;
    companyAddress?: string;
    returnDepartment?: string;
  }
) {
  const { ExchangeRequestManualApprovedEmail } = await import(
    './templates/exchange-request-manual-approved'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  // Only attempt PDF generation in server environments
  if (
    returnLabelData?.returnLabelOption === 'print' &&
    typeof window === 'undefined'
  ) {
    try {
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        customerName: returnLabelData.customerName,
        customerAddress: returnLabelData.customerAddress,
        companyName: returnLabelData.companyName,
        companyAddress: returnLabelData.companyAddress,
        returnDepartment: returnLabelData.returnDepartment,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  return sendEmail({
    to,
    subject: `Exchange Request #${returnNumber} Approved`,
    template: ExchangeRequestManualApprovedEmail({ returnNumber }),
    attachments,
  });
}

export async function sendRequestDeclinedEmail(
  to: string,
  returnNumber: string
) {
  const { RequestDeclinedEmail } = await import('./templates/request-declined');

  return sendEmail({
    to,
    subject: `Request #${returnNumber} Declined`,
    template: RequestDeclinedEmail({ returnNumber }),
  });
}

export async function sendAdminAddressChangeNotificationEmail(
  to: string,
  orderName: string,
  customerInfo: {
    firstName?: string;
    lastName?: string;
    email: string;
    phone?: string;
  },
  originalAddress: {
    firstName?: string;
    lastName?: string;
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    zip?: string;
    country?: string;
    phone?: string;
  },
  newAddress: {
    firstName?: string;
    lastName?: string;
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    zip?: string;
    country?: string;
    phone?: string;
  }
) {
  const { AdminAddressChangeNotificationTemplate } = await import(
    './templates/admin-address-change-notification'
  );

  return sendEmail({
    to,
    subject: `Address Change Request - Order ${orderName}`,
    template: AdminAddressChangeNotificationTemplate({
      orderName,
      customerInfo,
      originalAddress,
      newAddress,
    }),
  });
}

export async function sendAdminRequestNotificationEmail(
  to: string,
  orderName: string,
  customerInfo: {
    firstName?: string;
    lastName?: string;
    email: string;
    phone?: string;
  },
  requestType: string,
  requestDetails: string,
  returnItems: any[]
) {
  const { AdminRequestNotificationTemplate } = await import(
    './templates/admin-request-notification'
  );

  return sendEmail({
    to,
    subject: `${requestType} - Order ${orderName}`,
    template: AdminRequestNotificationTemplate({
      orderName,
      customerInfo,
      requestType,
      requestDetails,
      returnItems,
    }),
  });
}

export async function sendSendersShareEmail(
  to: string,
  shareId: string,
  senderEmail: string,
  title: string,
  message: string,
  fileCount: number,
  expiresAt: string,
  isPrivate: boolean
) {
  const { SendersShareEmail } = await import('./templates/senders-share');

  return sendEmail({
    to,
    subject: `You've received a new file share${isPrivate ? ' (Private)' : ''}`,
    template: SendersShareEmail({
      shareId,
      senderEmail,
      title,
      message,
      fileCount,
      expiresAt,
      isPrivate,
    }),
  });
}

export async function sendSendersConfirmationEmail(
  to: string,
  shareId: string,
  title: string,
  message: string,
  fileCount: number,
  expiresAt: string,
  isPrivate: boolean
) {
  const { SendersConfirmationEmail } = await import(
    './templates/senders-confirmation'
  );

  return sendEmail({
    to,
    subject: 'Your files have been sent successfully',
    template: SendersConfirmationEmail({
      shareId,
      title,
      message,
      fileCount,
      expiresAt,
      isPrivate,
    }),
  });
}
