import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import React from 'react';

type SendersShareEmailProps = {
  readonly shareId?: string;
  readonly senderEmail?: string;
  readonly title?: string;
  readonly message?: string;
  readonly fileCount?: number;
  readonly expiresAt?: string;
  readonly isPrivate?: boolean;
};

export const SendersShareEmail = ({
  shareId,
  senderEmail,
  title,
  message,
  fileCount,
  expiresAt,
  isPrivate,
}: SendersShareEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>
        {title ? `${title} - ` : ''}You've received a new file share{isPrivate ? ' (Private)' : ''}
      </Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Img
            className="mx-auto"
            src="https://www.senders.jp/logo.png"
            alt="Senders Logo"
          />
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                {title || 'You have received a new file share'}
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                {senderEmail ? `From: ${senderEmail}` : 'A sender has shared files with you.'}
              </Text>
              {message && (
                <>
                  <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">Message:</Text>
                  <Text className="m-0 mb-4 whitespace-pre-line text-zinc-700">{message}</Text>
                </>
              )}
              <Hr className="my-4" />
              <Text className="m-0 mb-2 text-zinc-700">
                <strong>Number of files:</strong> {fileCount ?? 'N/A'}
              </Text>
              {expiresAt && (
                <Text className="m-0 mb-2 text-zinc-700">
                  <strong>Expires at:</strong> {expiresAt}
                </Text>
              )}
              <Text className="m-0 mb-2 text-zinc-700">
                <strong>Share type:</strong> {isPrivate ? 'Private' : 'Public'}
              </Text>
              {shareId && (
                <Text className="m-0 mt-4 text-blue-600 underline">
                  <a href={`https://yourdomain.com/share/${shareId}`}>View Shared Files</a>
                </Text>
              )}
              <Hr className="my-4" />
              <Text className="m-0 text-sm text-zinc-500">
                This email was sent automatically. If you have any questions, please contact support.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleSendersShareEmail = () => (
  <SendersShareEmail
    shareId="SHARE-12345"
    senderEmail="<EMAIL>"
    title="Project Files"
    message="Here are the files for the project."
    fileCount={3}
    expiresAt="2024-07-01"
    isPrivate={true}
  />
);

export default ExampleSendersShareEmail; 