import {
  Body,
  Container,
  Head,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';
import type { EmailDictionary } from '../i18n';
import { interpolateEmailTemplate } from '../i18n';

type ExchangeRequestAutoApprovedEmailProps = {
  readonly returnNumber: string;
  readonly dictionary: EmailDictionary;
};

export const ExchangeRequestAutoApprovedEmail = ({
  returnNumber,
  dictionary,
}: ExchangeRequestAutoApprovedEmailProps) => {
  const t = dictionary.exchange_request_auto_approved;

  return (
    <Tailwind>
      <Html>
        <Head />
        <Preview>
          {interpolateEmailTemplate(t.preview, { returnNumber })}
        </Preview>
        <Body className="bg-zinc-50 font-sans">
          <Container className="mx-auto py-12">
            <Img
              className="mx-auto"
              src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
            />
            <Section className="mt-8 rounded-md bg-zinc-200 p-px">
              <Section className="rounded-[5px] bg-white p-8">
                <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                  {t.title}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  {interpolateEmailTemplate(t.message, { returnNumber })}
                </Text>
                <Text className="m-0 mb-4 text-zinc-700">
                  <strong>{t.next_steps}</strong> {t.next_steps_text}
                </Text>
                <Text className="m-0 text-zinc-700">
                  <strong>{t.replacement}</strong> {t.replacement_text}
                </Text>
              </Section>
            </Section>
          </Container>
        </Body>
      </Html>
    </Tailwind>
  );
};

const ExampleExchangeRequestAutoApprovedEmail = () => {
  // Mock dictionary for example
  const mockDictionary = {
    exchange_request_auto_approved: {
      preview: 'Your exchange request #{returnNumber} has been approved',
      title: 'Exchange Request Approved',
      message: 'Your exchange request #{returnNumber} has been approved.',
      next_steps: 'Next steps:',
      next_steps_text:
        'Please follow the return shipping instructions or use the prepaid label provided.',
      replacement: 'Replacement:',
      replacement_text:
        "We'll ship your replacement item once we receive your original item.",
    },
  } as EmailDictionary;

  return (
    <ExchangeRequestAutoApprovedEmail
      returnNumber="EXC-12345"
      dictionary={mockDictionary}
    />
  );
};

export default ExampleExchangeRequestAutoApprovedEmail;
