import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';

type CustomerInfo = {
  readonly firstName?: string;
  readonly lastName?: string;
  readonly email: string;
  readonly phone?: string;
};

type ReturnItems = {
  readonly title: string;
  readonly quantity: number;
  readonly price: number;
  readonly currency: string;
  readonly variantTitle?: string;
};

type AdminRequestNotificationProps = {
  readonly orderName: string;
  readonly customerInfo: CustomerInfo;
  readonly requestType: string;
  readonly requestDetails: string;
  readonly returnItems?: ReturnItems[];
};

export const AdminRequestNotificationTemplate = ({
  orderName,
  customerInfo,
  requestType,
  requestDetails,
  returnItems,
}: AdminRequestNotificationProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>
        {requestType.toUpperCase()} for order {orderName}
      </Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Img
            className="mx-auto"
            src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
          />
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950 capitalize">
                {requestType} Request Accepted
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                A customer has submitted a new request for their order.
              </Text>

              <Hr className="my-4" />

              <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">
                Order Information
              </Text>
              <Text className="m-0 mb-1 text-zinc-700">
                <strong>Order ID:</strong> {orderName}
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                <strong>Customer:</strong> {customerInfo.firstName}{' '}
                {customerInfo.lastName} ({customerInfo.email})
              </Text>

              <Hr className="my-4" />

              <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">
                Request Reason:
              </Text>
              <Text className="m-0 mb-4 whitespace-pre-line text-zinc-700">
                {requestDetails}
              </Text>

              {returnItems && returnItems.length > 0 && (
                <>
                  <Hr className="my-4" />
                  <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">
                    Return Items
                  </Text>
                  {returnItems.map((item, index) => (
                    <Text key={index} className="m-0 mb-1 text-zinc-700">
                      • {item.title} ({item.quantity} x {item.currency}{' '}
                      {item.price})
                      {item.variantTitle && ` - ${item.variantTitle}`}
                    </Text>
                  ))}
                </>
              )}

              <Hr className="my-4" />

              <Text className="m-0 text-sm text-zinc-500">
                This notification was automatically generated. Please review the
                request in the admin panel.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleAdminRequestNotification = () => (
  <AdminRequestNotificationTemplate
    orderName="#1001"
    customerInfo={{
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-123-4567',
    }}
    requestType="Return Request"
    requestDetails="Customer wants to return a t-shirt because it is the wrong size."
    returnItems={[
      {
        title: 'T-Shirt',
        quantity: 1,
        price: 25.0,
        currency: 'USD',
        variantTitle: 'Blue - Large',
      },
      {
        title: 'Jeans',
        quantity: 1,
        price: 50.0,
        currency: 'USD',
        variantTitle: 'Black - 32x32',
      },
    ]}
  />
);

export default ExampleAdminRequestNotification;
