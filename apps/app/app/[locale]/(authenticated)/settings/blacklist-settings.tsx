'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { toast } from '@repo/design-system/components/ui/sonner';
import type { Dictionary } from '@repo/internationalization';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import * as z from 'zod';
import {
  addEmailToBlacklist,
  getBlacklistedEmails,
  removeEmailFromBlacklist,
} from './actions';

type BlacklistSettingsProps = {
  dictionary: Dictionary;
};

export function BlacklistSettings({ dictionary }: BlacklistSettingsProps) {
  // Form validation schema
  const addEmailSchema = z.object({
    email: z
      .string()
      .min(1, dictionary.admin.settings.blacklist.email_required)
      .email(dictionary.admin.settings.blacklist.email_invalid)
      .toLowerCase(),
  });

  type AddEmailFormValues = z.infer<typeof addEmailSchema>;
  const [blacklistedEmails, setBlacklistedEmails] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize form with react-hook-form
  const form = useForm<AddEmailFormValues>({
    resolver: zodResolver(addEmailSchema),
    defaultValues: {
      email: '',
    },
  });

  useEffect(() => {
    loadBlacklistedEmails();
  }, []);

  const loadBlacklistedEmails = async () => {
    try {
      const emails = await getBlacklistedEmails();
      setBlacklistedEmails(emails);
    } catch (error) {
      console.error('Failed to load blacklisted emails:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddEmail = async (values: AddEmailFormValues) => {
    if (blacklistedEmails.includes(values.email)) {
      toast.error(dictionary.admin.settings.blacklist.email_exists);
      return;
    }

    try {
      await addEmailToBlacklist(values.email);
      setBlacklistedEmails([...blacklistedEmails, values.email]);
      form.reset();
      toast.success(dictionary.admin.settings.blacklist.add_success);
    } catch (error) {
      console.error('Failed to add email to blacklist:', error);
      toast.error(dictionary.admin.settings.blacklist.add_error);
    }
  };

  const handleRemoveEmail = async (email: string) => {
    try {
      await removeEmailFromBlacklist(email);
      setBlacklistedEmails(blacklistedEmails.filter((e) => e !== email));
      toast.success(dictionary.admin.settings.blacklist.remove_success);
    } catch (error) {
      console.error('Failed to remove email from blacklist:', error);
      toast.error(dictionary.admin.settings.blacklist.remove_error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.admin.settings.blacklist.title}</CardTitle>
        <CardDescription>
          {dictionary.admin.settings.blacklist.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleAddEmail)}
            className="flex gap-2"
          >
            <div className="flex-1">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {dictionary.admin.settings.blacklist.add_email_label}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder={
                          dictionary.admin.settings.blacklist.email_placeholder
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex items-end">
              <Button
                type="submit"
                disabled={form.formState.isSubmitting}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {dictionary.admin.settings.blacklist.add_button}
              </Button>
            </div>
          </form>
        </Form>

        <div>
          <Label>
            {dictionary.admin.settings.blacklist.blacklisted_emails.replace(
              '{count}',
              blacklistedEmails.length.toString()
            )}
          </Label>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            </div>
          ) : blacklistedEmails.length === 0 ? (
            <div className="py-8 text-center text-muted-foreground">
              {dictionary.admin.settings.blacklist.no_emails}
            </div>
          ) : (
            <div className="mt-2 flex flex-wrap gap-2">
              {blacklistedEmails.map((email) => (
                <Badge
                  key={email}
                  variant="secondary"
                  className="flex items-center gap-2"
                >
                  {email}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleRemoveEmail(email)}
                  >
                    <Trash2 className="h-2 w-2" />
                  </Button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
